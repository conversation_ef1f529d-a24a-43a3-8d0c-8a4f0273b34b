@using TeyaMobileViewModel.ViewModel
@using TeyaMobileModel.ViewModel
@using TeyaMobileModel.Model
@using TeyaUIViewModels.ViewModel
@using System.Text.Json
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject IOrganizationService OrganizationService
@inject IMemberService MemberService
@inject IRoleService RoleService
@inject ILogger<AuthenticationHandler> logger
@inject GraphApiService GraphService
@inject ActiveUser user
@inject IRoleslistService _RoleslistService

@* This component handles user registration after authentication *@
@* It's invisible to the user but runs the registration logic *@

@code {
    private bool _userRegistrationProcessed = false;
    private bool Subscription = false;
    private const int zero = 0;
    private Guid OrganizationId { get; set; }
    private List<String> roles;

    protected override async Task OnInitializedAsync()
    {
        await CheckAuthenticationAndRegisterUser();
    }

    private async Task CheckAuthenticationAndRegisterUser()
    {
        try
        {
            if (AuthService.IsAuthenticated && !_userRegistrationProcessed)
            {
                _userRegistrationProcessed = true;
                await HandleUserRegistration();
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in authentication handler: {Error}", ex.Message);
        }
    }

    private async Task HandleUserRegistration()
    {
        try
        {
            var email_Is_Present = await MemberService.SearchMembersEmailAsync(user.mail, OrganizationId, Subscription);
            if (email_Is_Present)
            {
                // User exists, redirect to appointments
                Navigation.NavigateTo("/appointments", forceLoad: true);
            }
            else
            {
                // First time user - register them
                await RegisterNewUser();
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in user registration: {Error}", ex.Message);
        }
    }

    private async Task RegisterNewUser()
    {
        try
        {
            await GraphService.GetLoggedInUserDetailsAsync();
            var result = await GraphService.GetUserDetailsAsync();

            if (result)
            {
                var userDetails = JsonSerializer.Deserialize<Dictionary<string, object>>(GraphService.UserDetails);

                try
                {
                    OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                }
                catch (KeyNotFoundException)
                {
                    logger.LogInformation("Organization '{OrganizationName}' not found, will be created during member registration", user.OrganizationName);
                    OrganizationId = Guid.Empty;
                }

                bool userExists = false;
                if (OrganizationId != Guid.Empty)
                {
                    userExists = await MemberService.SearchMembersEmailAsync(user.mail, OrganizationId, Subscription);
                }

                if (!userExists)
                {
                    var newMember = await CreateMemberFromUserDetails();
                    if (newMember != null)
                    {
                        var responseMessage = await MemberService.RegisterMembersContentAsync(new List<Member> { newMember });

                        if (responseMessage != null)
                        {
                            logger.LogInformation("Member registered successfully");
                            OrganizationId = newMember.OrganizationID ?? Guid.Empty;
                            
                            // Redirect to appointments after successful registration
                            Navigation.NavigateTo("/appointments", forceLoad: true);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error registering new user: {Error}", ex.Message);
        }
    }

    private async Task<Member> CreateMemberFromUserDetails()
    {
        TeyaMobileModel.Model.Organization organization = null;
        Member registeredMember = null;

        try
        {
            var organizationDetails = await OrganizationService.GetOrganizationsByNameAsync(user.OrganizationName);
            organization = organizationDetails.FirstOrDefault();

            if (organization == null)
            {
                organization = new TeyaMobileModel.Model.Organization
                {
                    OrganizationId = Guid.NewGuid(),
                    OrganizationName = user.OrganizationName
                };
                roles = await _RoleslistService.GetAllRoleNamesAsync();
                var createdOrganization = await OrganizationService.RegisterOrganizationsAsync(organization);

                if (createdOrganization == null)
                {
                    logger.LogError("Organization creation failed");
                    throw new Exception("Organization creation failed");
                }

                logger.LogInformation("New organization created: {OrganizationId}", createdOrganization.OrganizationId);
            }

            registeredMember = new Member
            {
                Id = Guid.TryParse(user.id, out var userId) ? userId : Guid.NewGuid(),
                Email = user.mail,
                FirstName = user.givenName,
                LastName = user.surname,
                UserName = $"{user.givenName}{user.surname}",
                PhoneNumber = user.mobilePhone,
                Country = user.country,
                OrganizationID = organization.OrganizationId,
                OrganizationName = user.OrganizationName,
                Subscription = Subscription,
                Address = new Address
                {
                    AddressLine1 = user.streetAddress,
                    PostalCode = user.postalCode,
                    State = user.state,
                    Country = user.country,
                    Subscription = Subscription,
                },
                IsActive = true
            };

            var rolesdata = await RoleService.GetAllRolesByOrgIdAsync(organization.OrganizationId, Subscription);
            if (rolesdata.Count == zero)
            {
                foreach (var role in roles)
                {
                    var newRole = new Role
                    {
                        RoleId = Guid.NewGuid(),
                        RoleName = role,
                        CreatedDate = DateTime.Now,
                        IsActive = true,
                        UpdatedDate = DateTime.Now,
                        UpdatedBy = Guid.Parse(user.id),
                        OrganizationID = organization.OrganizationId,
                        Subscription = Subscription
                    };
                    await RoleService.RegisterRoleAsync(newRole);

                    if (role == "Admin")
                    {
                        registeredMember.RoleID = newRole.RoleId;
                        registeredMember.RoleName = "Admin";
                    }
                }
            }
            else
            {
                var adminRole = rolesdata.FirstOrDefault(r => r.RoleName == "Admin");
                if (adminRole != null)
                {
                    registeredMember.RoleID = adminRole.RoleId;
                    registeredMember.RoleName = "Admin";
                }
            }

            var updateFields = new Dictionary<string, object>
            {
                { "displayName", registeredMember.UserName }
            };

            bool updateSuccessful = await GraphService.UpdateUserProfileAsync(user.id, updateFields);
            if (!updateSuccessful)
            {
                logger.LogError("Failed to update display name");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing organization: {Error}", ex.Message);
        }

        return registeredMember;
    }
}
