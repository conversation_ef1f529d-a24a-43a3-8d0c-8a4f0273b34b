using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Web;
using TeyaMobile.Shared.Services;
using System.Security.Claims;

namespace TeyaMobile.Web.Services
{
    /// <summary>
    /// Simplified web authentication service using Microsoft Identity
    /// </summary>
    public class SimpleWebAuthenticationService : TeyaMobileViewModel.ViewModel.IAuthenticationService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SimpleWebAuthenticationService> _logger;
        private readonly ITokenAcquisition _tokenAcquisition;

        public SimpleWebAuthenticationService(
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider,
            ILogger<SimpleWebAuthenticationService> logger,
            ITokenAcquisition tokenAcquisition)
        {
            _httpContextAccessor = httpContextAccessor;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _tokenAcquisition = tokenAcquisition;
        }

        public bool IsAuthenticated =>
            _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

        public Task<bool> LoginAsync()
        {
            try
            {
                // Check if user is already authenticated
                if (IsAuthenticated)
                {
                    return Task.FromResult(true);
                }

                var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                // Trigger the authentication challenge
                navigationManager.NavigateTo("/MicrosoftIdentity/Account/SignIn", forceLoad: true);

                // Return false since authentication is in progress via redirect
                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return Task.FromResult(false);
            }
        }

        public Task LogoutAsync()
        {
            try
            {
                var navigationManager = _serviceProvider.GetRequiredService<NavigationManager>();

                // Trigger the logout
                navigationManager.NavigateTo("/MicrosoftIdentity/Account/SignOut", forceLoad: true);

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null || !IsAuthenticated)
                    return string.Empty;

                // First try to get token using Microsoft Identity Web token acquisition
                // This automatically handles refresh if the token is expired
                try
                {
                    var scopes = new[] { "api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user" };
                    var accessToken = await _tokenAcquisition.GetAccessTokenForUserAsync(scopes);
                    if (!string.IsNullOrEmpty(accessToken))
                        return accessToken;
                }
                catch (Exception tokenEx)
                {
                    _logger.LogWarning(tokenEx, "Token acquisition failed, falling back to stored token");
                }

                // Fallback: Get access token from authentication properties
                var storedToken = await httpContext.GetTokenAsync("access_token");
                return storedToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token");
                return string.Empty;
            }
        }

        public Task<string> GetUserNameAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                    return Task.FromResult(string.Empty);

                var claims = httpContext.User.Claims;
                var displayName = claims.FirstOrDefault(c => c.Type == "name")?.Value ??
                                 claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value ?? 
                                 string.Empty;

                return Task.FromResult(displayName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user name");
                return Task.FromResult(string.Empty);
            }
        }

        public Task<string> GetUserEmailAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.User?.Identity?.IsAuthenticated != true)
                    return Task.FromResult(string.Empty);

                var claims = httpContext.User.Claims;
                var email = claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value ??
                           claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ??
                           string.Empty;

                return Task.FromResult(email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email");
                return Task.FromResult(string.Empty);
            }
        }

        public async Task<string> GetGraphApiScopeAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null || !IsAuthenticated)
                    return string.Empty;

                // Get Graph API scope from configuration
                var configuration = _serviceProvider.GetRequiredService<IConfiguration>();
                var graphScope = configuration["Graph_Auth_Scope"] ?? "https://graph.microsoft.com/.default";
                var graphScopes = new[] { graphScope };

                // Use Microsoft Identity Web token acquisition for Graph API scope
                var accessToken = await _tokenAcquisition.GetAccessTokenForUserAsync(graphScopes);
                return accessToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Graph API access token: {Error}", ex.Message);
                return string.Empty;
            }
        }
    }
}
