using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using System.Text.Json;
using TeyaMobile.Shared.Services;
using TeyaMobileModel.Model;
using TeyaMobileModel.ViewModel;
using TeyaMobileViewModel.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;

namespace TeyaMobile.Shared.Components.Authentication
{
    public partial class LoginComponent : ComponentBase
    {
        private List<String> roles;
        [Inject] public IAuthenticationService AuthService { get; set; } = default!;
        [Inject] public NavigationManager Navigation { get; set; } = default!;
        [Inject] public IOrganizationService OrganizationService { get; set; } = default!;
        [Inject] public IMemberService MemberService { get; set; } = default!;
        [Inject] public IRoleService RoleService { get; set; } = default!;
        [Inject] private ILogger<LoginComponent> logger { get; set; }
        [Inject] private GraphApiService GraphService { get; set; } = default!;
        [Inject] private ActiveUser user { get; set; }
        [Inject] private IRoleslistService _RoleslistService { get; set; } = default!;
        private bool IsLoading = false;
        private bool IsAuthenticated = false;
        private string UserName = string.Empty;
        private string UserEmail = string.Empty;
        private string ErrorMessage = string.Empty;
        private string email = string.Empty;
        private bool Subscription = false;
        private const int zero = 0;

        private Guid OrganizationId { get; set; }
        protected override async Task OnInitializedAsync()
        {
            await CheckAuthenticationState();
        }

        private async Task CheckAuthenticationState()
        {
            try
            {
                IsAuthenticated = AuthService.IsAuthenticated;

                if (IsAuthenticated)
                {
                    UserName = await AuthService.GetUserNameAsync();
                    UserEmail = await AuthService.GetUserEmailAsync();
                    email = UserEmail;
                }

                ErrorMessage = string.Empty;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error checking authentication: {ex.Message}";
                StateHasChanged();
            }
        }

        private async Task HandleLogin()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                StateHasChanged();

                var success = await AuthService.LoginAsync();

                if (success)
                {
                    //Navigation.NavigateTo("/", forceLoad: true);
                    await HandleLoginClick();
                }
                else
                {
                    IsLoading = false;
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Login failed: {ex.Message}";
                IsLoading = false;
                StateHasChanged();
            }
        }

        private async Task HandleLogout()
        {
            try
            {
                IsLoading = true;
                StateHasChanged();

                await AuthService.LogoutAsync();

                IsAuthenticated = false;
                UserName = string.Empty;
                UserEmail = string.Empty;
                ErrorMessage = string.Empty;
                IsLoading = false;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Logout failed: {ex.Message}";
                IsLoading = false;
                StateHasChanged();
            }
        }
        public async Task HandleLoginClick()
        {
            var email_Is_Present = await MemberService.SearchMembersEmailAsync(user.mail, OrganizationId, Subscription);
            if (email_Is_Present)
            {
                Navigation.NavigateTo("/appointments", forceLoad: true);
            }
            else
            {
                //First Time User Logged In Populate there details in the database
                try
                {
                    await GraphService.GetLoggedInUserDetailsAsync();
                    var result = await GraphService.GetUserDetailsAsync();

                    if (result)
                    {
                        var userDetails = JsonSerializer.Deserialize<Dictionary<string, object>>(GraphService.UserDetails);
                        //WillCheck later if this is needed
                        // TokenService.UserDetails = AuthService.UserDetails; 

                        try
                        {
                            OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(user.OrganizationName);
                        }
                        catch (KeyNotFoundException)
                        {
                            logger.LogInformation("Organization '{OrganizationName}' not found, will be created during member registration", user.OrganizationName);
                            OrganizationId = Guid.Empty;
                        }

                        bool userExists = false;
                        if (OrganizationId != Guid.Empty)
                        {
                            userExists = await MemberService.SearchMembersEmailAsync(user.mail, OrganizationId, Subscription);
                        }

                        if (!userExists)
                        {
                            var newMember = await CreateMemberFromUserDetails();
                            if (newMember != null)
                            {
                                var responseMessage = await MemberService.RegisterMembersContentAsync(new List<Member> { newMember });

                                if (responseMessage != null)
                                {
                                    logger.LogInformation("Member registered successfully");
                                    OrganizationId = newMember.OrganizationID ?? Guid.Empty;
                                }
                            }
                        }
                    }
                    //else I think it is not required to set UserDetailsFetchFailed in GraphService
                    //{
                    //    GraphService.UserDetails = "UserDetailsFetchFailed";
                    //}
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error fetching or registering user");
                }

            }
        }
        private async Task<Member> CreateMemberFromUserDetails()
        {
            TeyaMobileModel.Model.Organization organization = null;
            Member registeredMember = null;

            try
            {
                var organizationDetails = await OrganizationService.GetOrganizationsByNameAsync(user.OrganizationName);
                organization = organizationDetails.FirstOrDefault();

                if (organization == null)
                {
                    organization = new TeyaMobileModel.Model.Organization
                    {
                        OrganizationId = Guid.NewGuid(),
                        OrganizationName = user.OrganizationName
                    };
                    roles = await _RoleslistService.GetAllRoleNamesAsync();
                    var createdOrganization = await OrganizationService.RegisterOrganizationsAsync(organization);

                    if (createdOrganization == null)
                    {
                        logger.LogError("OrganizationCreationFailed");
                        throw new Exception("OrganizationCreationFailedMessage");
                    }

                    logger.LogInformation("NewOrganizationCreated", createdOrganization.OrganizationId);
                }

                registeredMember = new Member
                {
                    Id = Guid.TryParse(user.id, out var userId) ? userId : Guid.NewGuid(),
                    Email = user.mail,
                    FirstName = user.givenName,
                    LastName = user.surname,
                    UserName = $"{user.givenName}{user.surname}",
                    PhoneNumber = user.mobilePhone,
                    Country = user.country,
                    OrganizationID = organization.OrganizationId,
                    OrganizationName = user.OrganizationName,
                    Subscription = Subscription,
                    Address = new Address
                    {
                        AddressLine1 = user.streetAddress,
                        PostalCode = user.postalCode,
                        State = user.state,
                        Country = user.country,
                        Subscription = Subscription,
                    },
                    IsActive = true
                };

                var rolesdata = await RoleService.GetAllRolesByOrgIdAsync(organization.OrganizationId, Subscription);
                if (rolesdata.Count == zero)
                {
                    foreach (var role in roles)
                    {
                        var newRole = new Role
                        {
                            RoleId = Guid.NewGuid(),
                            RoleName = role,
                            CreatedDate = DateTime.Now,
                            IsActive = true,
                            UpdatedDate = DateTime.Now,
                            UpdatedBy = Guid.Parse(user.id),
                            OrganizationID = organization.OrganizationId,
                            Subscription = Subscription
                        };
                        await RoleService.RegisterRoleAsync(newRole);

                        if (role == "Admin")
                        {
                            registeredMember.RoleID = newRole.RoleId;
                            registeredMember.RoleName = "Admin";
                        }
                    }
                    //var pagePaths = await PagePathService.GetPagePathsAsync();
                    //var pageUrls = pagePaths.Select(p => new PageRoleMappingData
                    //{
                    //    PagePath = p.PagePathValue
                    //}).ToList();
                    //foreach (var page in pageUrls)
                    //{
                    //    var newMapping = new PageRoleMappingData
                    //    {
                    //        Id = Guid.NewGuid(),
                    //        PagePath = page.PagePath,
                    //        RoleId = registeredMember.RoleID ?? Guid.Empty,
                    //        RoleName = registeredMember.RoleName,
                    //        OrganizationID = organization.OrganizationId,
                    //        CreatedBy = Guid.Parse(user.id),
                    //        CreatedDate = DateTime.Now,
                    //        IsActive = true,
                    //        HasAccess = true,
                    //        Subscription = Subscription
                    //    };

                    //await PageRoleMappingService.AddPageRoleMappingAsync(newMapping);
                    //}

                    //logger.LogInformation(Localizer["PageRoleMappingsCreatedSuccessfully"]);
                }
                else
                {
                    var adminRole = rolesdata.FirstOrDefault(r => r.RoleName == "Admin");
                    if (adminRole != null)
                    {
                        registeredMember.RoleID = adminRole.RoleId;
                        registeredMember.RoleName = "Admin";
                    }
                }

                //predefinedvisittypes = await _PredefinedVisitTypeService.GetAllPredefinedVisitTypesAsync();
                //visittypes = await _VisitTypeService.GetVisitTypesByOrganizationIdAsync(organization.OrganizationId, Subscription);

                //if (visittypes == null || !visittypes.Any())
                //{
                //    foreach (var predefined in predefinedvisittypes)
                //    {
                //        var newVisit = new VisitType
                //        {
                //            ID = Guid.NewGuid(),
                //            OrganizationId = organization.OrganizationId,
                //            VisitName = predefined.VisitName,
                //            CPTCode = predefined.CPTCode,
                //            Subscription = Subscription
                //        };
                //        await _VisitTypeService.AddVisitTypeAsync(newVisit);
                //    }
                //}

                var updateFields = new Dictionary<string, object>
                {
                { "displayName", registeredMember.UserName }
                };

                bool updateSuccessful = await GraphService.UpdateUserProfileAsync(user.id, updateFields);
                if (!updateSuccessful)
                {
                    logger.LogError("FailedToUpdateDisplayName");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "ErrorProcessingOrganization", ex.Message);
            }

            return registeredMember;
        }
        private void ClearError()
        {
            ErrorMessage = string.Empty;
            StateHasChanged();
        }
    }
}
