using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Components.Server;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Syncfusion.Blazor;
using Syncfusion.Licensing;
using System.Text.Json;
using System.Text.Json.Serialization;
using TeyaMobile.Shared.Pages;
using TeyaMobile.Shared.Services;
using TeyaMobile.Shared.Services.TeyaMobile.Shared.Services;
using TeyaMobile.Web.Components;
using TeyaMobile.Web.Services;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;
using TeyaWebApp.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaMobileModel.ViewModel;

var builder = WebApplication.CreateBuilder(args);

var syncfusionKey = builder.Configuration["SyncfusionKey"];
if (!string.IsNullOrEmpty(syncfusionKey))
{
    SyncfusionLicenseProvider.RegisterLicense(syncfusionKey);
}

builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Microsoft Identity authentication with token acquisition
builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"))
    .EnableTokenAcquisitionToCallDownstreamApi()
    .AddInMemoryTokenCaches();

// Configure OpenID Connect options
builder.Services.Configure<OpenIdConnectOptions>(OpenIdConnectDefaults.AuthenticationScheme, options =>
{
    // Set the correct callback path
    options.CallbackPath = "/signin-oidc";
    options.SignedOutCallbackPath = "/signout-callback-oidc";

    // Save tokens to authentication properties - THIS IS CRITICAL FOR TOKEN RETRIEVAL
    options.SaveTokens = true;

    // Configure token refresh
    options.UseTokenLifetime = false; // Don't use token lifetime for authentication cookie expiration

    // Request the required scopes
    options.Scope.Clear();
    options.Scope.Add("openid");
    options.Scope.Add("profile");
    options.Scope.Add("email");
    options.Scope.Add("api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user");
    options.Scope.Add("https://graph.microsoft.com/.default"); // Add Graph API scope

    // Configure response type for better token handling
    options.ResponseType = "code";
    options.UsePkce = true;

    // Enable refresh token handling
    options.Scope.Add("offline_access"); // Required for refresh tokens

    // Configure token refresh behavior - DISABLE UserInfo endpoint for CIAM
    options.GetClaimsFromUserInfoEndpoint = false; // CIAM doesn't support UserInfo endpoint
    options.MapInboundClaims = false; // Don't map claims to legacy format

    options.Events.OnRedirectToIdentityProvider = context =>
    {
        // Ensure we use the correct redirect URI
        context.ProtocolMessage.RedirectUri = context.Request.Scheme + "://" + context.Request.Host + "/signin-oidc";
        return Task.CompletedTask;
    };

    options.Events.OnRedirectToIdentityProviderForSignOut = context =>
    {
        // Ensure we use the correct post logout redirect URI
        context.ProtocolMessage.PostLogoutRedirectUri = context.Request.Scheme + "://" + context.Request.Host + "/signout-callback-oidc";
        return Task.CompletedTask;
    };

    options.Events.OnAuthenticationFailed = context =>
    {
        context.Response.Redirect("/MicrosoftIdentity/Account/AccessDenied");
        context.HandleResponse();
        return Task.CompletedTask;
    };

    options.Events.OnTicketReceived = context =>
    {
        // Let the default flow continue - don't force redirect here
        // The authentication middleware will handle the redirect properly
        return Task.CompletedTask;
    };

    // Add token refresh event handling
    options.Events.OnTokenValidated = context =>
    {
        // Token validated successfully - this is where refresh tokens are also stored
        return Task.CompletedTask;
    };

    options.Events.OnAccessDenied = context =>
    {
        // Handle access denied - redirect to home page for re-authentication
        context.Response.Redirect("/");
        context.HandleResponse();
        return Task.CompletedTask;
    };

    // Handle authentication failures
    options.Events.OnRemoteFailure = context =>
    {
        // Log the error and redirect to home page
        context.Response.Redirect("/");
        context.HandleResponse();
        return Task.CompletedTask;
    };
});

// Add Microsoft Identity UI
builder.Services.AddRazorPages()
    .AddMicrosoftIdentityUI();

// Authorization
builder.Services.AddAuthorization();

// HTTP Context Accessor
builder.Services.AddHttpContextAccessor();

builder.Services.AddHttpClient();
builder.Services.AddLocalization();
builder.Services.AddScoped<ISpeechService, SpeechService>();
builder.Services.AddScoped<AudioRecorderComponent>();
builder.Services.AddScoped<IAudioRecorder, DummyAudioRecorder>();
builder.Services.AddScoped<IAppointmentService, AppointmentService>();
builder.Services.AddScoped<IProgressNotesService, ProgressNotesService>();
builder.Services.Configure<JsonSerializerOptions>(options =>
{
    options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.Converters.Add(new ActiveUserConverter());
});
// Authentication service
builder.Services.AddScoped<TeyaMobileViewModel.ViewModel.IAuthenticationService, SimpleWebAuthenticationService>();
builder.Services.AddScoped<TeyaMobileModel.ViewModel.IOrganizationService, OrganizationService>();
builder.Services.AddScoped<TeyaMobileModel.Model.ActiveUser>();
builder.Services.AddScoped<TeyaUIViewModels.ViewModel.IRoleslistService, RoleslistService>();
builder.Services.AddScoped<TeyaMobileModel.ViewModel.IRoleService,RoleService>();
builder.Services.AddScoped<GraphApiService>();
builder.Services.AddScoped<IMemberService,MemberService>();
builder.Services.AddSingleton<IFormFactor, FormFactor>();
builder.Services.AddScoped<TeyaMobileViewModel.ViewModel.IGraphAdminService,GraphAdminService>();
// Load environment variables from .env file
DotNetEnv.Env.Load();

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();
builder.Services.AddSyncfusionBlazor();

// Enhanced logging for SignalR and Blazor Server debugging
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.SetMinimumLevel(LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.SignalR", LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.Http.Connections", LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.Components.Server.Circuits", LogLevel.Debug);
}

var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddAdditionalAssemblies(typeof(TeyaMobile.Shared._Imports).Assembly);

// Map Microsoft Identity routes for authentication
app.MapControllers();
app.MapRazorPages();

app.Run();
