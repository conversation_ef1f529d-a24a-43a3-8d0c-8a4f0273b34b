@page "/"
@using TeyaMobile.Shared.Services
@using TeyaMobile.Shared.Components.Authentication
@inject IFormFactor FormFactor

<PageTitle>Home</PageTitle>

<h1>Hello, world!</h1>

Welcome to your new app running on <em>@factor</em> using <em>@platform</em>.

<!-- Add authentication handler component -->
<AuthenticationHandler />

@code {
    private string factor => FormFactor.GetFormFactor();
    private string platform => FormFactor.GetPlatform();
}
