AZURE_SPEECH_API_KEY=91SxUNRBf4Q5jx9PUNcFCAXwDjymNH7yoqudOgaYX9vYtWGP0JIZJQQJ99ALACYeBjFXJ3w3AAAYACOGwBTm
AZURE_REGION=eastus
AZURE_LANGUAGE=en-US
AZURE_FAST_TRANSCRIPTION_URL=https://eastus.api.cognitive.microsoft.com/speechtotext/transcriptions:transcribe?api-version=2024-11-15

RxNormDrugListUrl = https://rxnav.nlm.nih.gov/REST/Prescribe/allconcepts.json?tty=IN
RxNormBrandListUrl = https://rxnav.nlm.nih.gov/REST/Prescribe/allconcepts.json?tty=BN
RxNormBrandSBDListUrl = https://rxnav.nlm.nih.gov/REST/Prescribe/drugs.json
RxNormDrugListUrl = https://rxnav.nlm.nih.gov/REST/Prescribe/allconcepts.json?tty=IN
SPEECH_API=http://localhost/EncounterNotesService/Speech
PrescriptionMedicationUrl=http://localhost/EncounterNotesService/api/PrescriptionMedication
RegistrationUrl=http://localhost/MemberServiceApi/api/Registration/registration
OrganizationsUrl=http://localhost/MemberServiceApi/api/Organizations
FacilitiesUrl=http://localhost/MemberServiceApi/api/Facility
FACILITY_URL=http://localhost/MemberServiceApi/api/Facility
USER_THEME_URL=http://localhost/MemberServiceApi/api/UserTheme
DELETE_USER_THEME_URL=http://localhost/MemberServiceApi/api/UserTheme
PAGE_ROLE_MAPPING_URL=http://localhost/MemberServiceApi/api/PageRoleMapping
DELETE_PAGE_ROLE_MAPPING_URL=http://localhost/MemberServiceApi/api/PageRoleMapping
_registrationOrganizationUrl=http://localhost/MemberServiceApi/api/Organizations
RolesUrl=http://localhost/MemberServiceApi/api/Roles
MembersUrl=http://localhost/MemberServiceApi/api/Products/{productId}/members
ProductsUrl= http://localhost/MemberServiceApi/api/Products
UpdateAccessUrl= http://localhost/MemberServiceApi/api/Products/updateAccess
LicenseUrl= http://localhost/MemberServiceApi/api/Licenses
UpdateLicenseAccessUrl=http://localhost/MemberServiceApi/api/Licenses/updateAccess
AppointmentsUrl= http://localhost/Appointments/api/Appointments
RecordsUrl=http://localhost/EncounterNotesService/api/Records
AudioUrl=https://teyarecordingsdev.blob.core.windows.net/audiofiles
AppointmentRegistration=http://localhost/Appointments/api/Appointments/appointmentRecord
MemberRegistration=http://localhost/MemberServiceApi/api/Registration
SyncfusionKey=MzU3MTQ3NUAzMjM3MmUzMDJlMzBVaUhlNTJjTEcwd00vVGtWWnh1ZmVxSWlMa3IwUlcvM2xlQzF3cklPUXFrPQ==
RedisConnectionString=TeyaHealthRedisCache.redis.cache.windows.net:6380,password=lyvUJ8XxAnKfamb8vvRKfl2tSwZreIrRAAzCaPaM4w0=,ssl=True,abortConnect=False
TasksUrl= http://localhost/PracticeApi/api/Practice

ProductId = http://localhost/MemberServiceApi/api/Products
TasksRegistration=http://localhost/PracticeApi/api/Practice/registration
PeopleUrl=http://localhost/PracticeApi/api/People
TemplatesUrl=http://localhost/EncounterNotesService/api/Templates
TemplatesInsertion=http://localhost/EncounterNotesService/api/Templates/Templates
PredefinedTemplatesUrl=http://localhost/EncounterNotesService/api/PredefinedTemplates
PredefinedTemplatesInsertion=http://localhost/EncounterNotesService/api/PredefinedTemplates/Templates
ProductId=161C1036-668A-4136-8B19-3F2A66E0D560
AZURE_BLOB_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=teyarecordingsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
AZURE_BLOB_CONTAINER_NAME=audiofiles
DELETE_REGISTRATION_MEMBER_URL=http://localhost/MemberServiceApi/api/Registration
REGISTRATION_MEMBER_URL=http://localhost/MemberServiceApi/api/Registration
DELETE_REGISTRATION_Organization_URL=http://localhost/MemberServiceApi/api/Organizations
DELETE_ROLE_URL=http://localhost/MemberServiceApi/api/Roles/
DELETE_FACILITY_URL=http://localhost/MemberServiceApi/api/Facility
ROLE_URL=http://localhost/MemberServiceApi/api/Roles/
ADD_GUARDIAN_URL=http://localhost/MemberServiceApi/api/Registration/add-guardian
REGISTRATION_Organization_URL=http://localhost/MemberServiceApi/api/Organizations
GET_GUARDIAN_BY_ID_URL=http://localhost/MemberServiceApi/api/Registration/get-guardian
UPLOAD_URL=http://localhost/EncounterNotesService/Speech/upload
GRAPH_API_BASE_URL=https://graph.microsoft.com/v1.0/

EncounterNotesURL = http://localhost/EncounterNotesService
PracticeApiURL = http://localhost/PracticeApi 
AppointmentsURL = http://localhost/Appointments 
MemberServiceURL = http://localhost/MemberServiceApi
MemberServiceURL = https://memberserviceapi-dev.ambitiousdesert-3f37926b.eastus.azurecontainerapps.io
AlertsServiceURL = http://localhost/AlertsApi
RxNormUrl = https://rxnav.nlm.nih.gov/REST/Prescribe
AlertsAPIURL = http://localhost/AlertsApi

AUTH_CLIENT_ID=e21369d6-92b3-446b-b981-0291bcb29b1b
AUTH_AUTHORITY=https://TeyaHealthDevAuth.ciamlogin.com/03a052f6-4a19-4ae7-8ed7-47b794e0e597/v2.0
AUTH_CLIENT_SECRET=****************************************
AUTH_RESPONSE_TYPE=code
AUTH_SAVE_TOKENS=true
AUTH_CALLBACK_PATH=/signin-oidc
AUTH_SCOPE_0=api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user
AUTH_SCOPE_1=openid
AUTH_SCOPE_2=profile
AUTH_SCOPE_3=api://e21369d6-92b3-446b-b981-0291bcb29b1b/access_as_user
AUTH_SCOPE_4=https://graph.microsoft.com/.default

EMAIL_SERVICE_CONNECTION_STRING=endpoint=https://emailservice-dev.unitedstates.communication.azure.com/;accesskey=452xhwCL3T6gP7eFUA6nXf8Cb9PTmU6aeJdCPTXMgWRzPemihAzoJQQJ99BAACULyCpSlSHjAAAAAZCS5bjb

AzureBusConnectionString=Endpoint=sb://teyahealth-dev.servicebus.windows.net/;SharedAccessKeyName=Task;SharedAccessKey=ZEy2ic97cvTOVqSjOxSEWSyiqN/8cRPDS+ASbOYB2DM=;EntityPath=tasks
TopicName=tasks

PrimaryDomain=TeyaHealthDevAuth.onmicrosoft.com
ROLE_SEARCH_URL=http://localhost/MemberServiceApi/api/Roles/search
ORGANIZATIONS_API_URL=http://localhost/MemberServiceApi/api/Organizations/search
AUTH_LOGOUT_URI=https://TeyaHealthDevAuth.ciamlogin.com/03a052f6-4a19-4ae7-8ed7-47b794e0e597/oauth2/v2.0/logout
AUTH_POST_LOGOUT_URI=https://localhost:7170/

APP_LOGIN_URL=https://dev.teyahealth.com

ServicePrincipleClientId=d20b72c2-619c-4b74-bb31-2194e8e5a137
ServicePrincipleTenantId=03a052f6-4a19-4ae7-8ed7-47b794e0e597 
ServicePrincipleSecret=****************************************


EXTENSION_ID_ORGANIZATION_NAME=extension_8a2d87f30a864e7e8b70f49083a6ff68_OrganizationName
EXTENSION_ID_ADDRESS=extension_8a2d87f30a864e7e8b70f49083a6ff68_Address
EXTENSION_PREFIX=extension_8a2d87f30a864e7e8b70f49083a6ff68

OPENAI_MODEL_NAME=gpt-4o-standard
OPENAI_ENDPOINT=https://teyaai-development.openai.azure.com/
OPENAI_API_KEY=GF8DM32OhU7yuEFqGgNIMSEcnoOSS4IyUVvGqtuP0GiEOAxKPaBFJQQJ99BAACYeBjFXJ3w3AAABACOG7tNX
